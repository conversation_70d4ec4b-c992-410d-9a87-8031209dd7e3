I/flutter ( 4278): PlayerControllerService: Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): PlayerControllerService: System health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 257724, memory_available: 3819544, memory_used: 7643316, memory_usage_percentage: 96.74, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 68G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:21:12.350218Z}
I/flutter ( 4278): [SupabaseService] Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515 to 2025-06-01T05:21:12.350742Z
I/flutter ( 4278): [SupabaseService] Health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 257724, memory_available: 3819544, memory_used: 7643316, memory_usage_percentage: 96.74, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 68G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:21:12.350218Z}
I/flutter ( 4278): PlayerControllerService: Successfully updated screen details
I/flutter ( 4278): PlayerControllerService: Received realtime update payload: PostgresChangePayload(schema: public, table: screens, commitTimestamp: 2025-06-01 05:21:13.070Z, eventType: PostgresChangeEvent.update, newRow: {code: 01295, created_at: 2025-04-28T04:30:09.978743+00:00, end_time: 22:00:00, health: {app_version: 0.1.0, build_number: 1, external_storage_available: 44G, external_storage_total: 111G, external_storage_usage_percentage: 61, external_storage_used: 68G, internal_storage_available: 44G, internal_storage_total: 111G, internal_storage_usage_percentage: 61, internal_storage_used: 67G, memory_available: 3819544, memory_free: 257724, memory_total: 7901040, memory_usage_percentage: 96.74, memory_used: 7643316, package_name: com.app.signage, platform: Android, timestamp: 2025-06-01T05:21:12.350218Z}, id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, is_deleted: false, is_registered: true, last_ping_at: 2025-06-01T05:21:12.350742+00:00, location: Main Office Building 1, name: Lobby Screen 1, site_email: , start_time: 08:00:
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: New row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: Old row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: New update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: Old update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: New is_deleted: false
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: No Updates
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): stop(1427): called with 289792 frames delivered
I/flutter ( 4278): PlatformVideoWidget: Android video completed
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: BetterWithPepsi.mp4
I/flutter ( 4278): ID: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): Media Type: Video
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Moving from schedule item 0 to 1
I/flutter ( 4278): New schedule item: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): Current content: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Next content: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SimpleMediaWidget for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): File Path: /storage/emulated/0/Android/data/com.app.signage/files/signage/content/FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): Is Image: false, Is Video: true
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): LoggingService: Logged simple media display: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 104a97fe-2928-4a35-b785-fbdc83765845, logDatetime: 2025-06-01 05:21:15.945564Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 104a97fe-2928-4a35-b785-fbdc83765845, logDatetime: 2025-06-01 05:21:15.945564Z)
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:21:15.945564Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 104a97fe-2928-4a35-b785-fbdc83765845}
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): SimpleMediaWidget: initState for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
I/ExoPlayerImpl( 4278): Init afbf81d [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29]
I/flutter ( 4278): Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
D/MediaCodec( 4278):  name = OMX.qcom.video.decoder.avc
D/AudioTrack( 4278): set(): streamType -1, sampleRate 48000, format 0x1, channelMask 0x3, frameCount 15376, flags #0, notificationFrames 0, sessionId 3601, transferType 3, uid -1, pid -1
I/OMXClient( 4278): IOmx service obtained
I/flutter ( 4278): Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
D/AudioTrack( 4278): Uid 10847 AudioTrack::setVolume left 1.000000 right 1.000000
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/SurfaceUtils( 4278): connecting to surface 0x7b479aa010, reason connectToSurface
I/MediaCodec( 4278): [OMX.qcom.video.decoder.avc] setting surface generation to 4380681
D/SurfaceUtils( 4278): disconnecting from surface 0x7b479aa010, reason connectToSurface(reconnect)
D/SurfaceUtils( 4278): connecting to surface 0x7b479aa010, reason connectToSurface(reconnect)
I/ExtendedACodec( 4278): setupVideoDecoder()
I/ExtendedACodec( 4278): Decoder will be in frame by frame mode
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1140, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 104a97fe-2928-4a35-b785-fbdc83765845, log_datetime: 2025-06-01T05:21:15.945564+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
I/flutter ( 4278): Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Next content ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Displaying content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): PlatformVideoWidget: Disposing video player
I/flutter ( 4278): SimpleMediaWidget: dispose for BetterWithPepsi.mp4
D/SurfaceUtils( 4278): set up nativeWindow 0x7b479aa010 for 1920x540, color 0x7fa30c06, rotation 0, usage 0x20002900
I/ExoPlayerImpl( 4278): Release 1306e8a [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29] [media3.common, media3.exoplayer, media3.decoder, media3.datasource, media3.extractor]
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/chatty  ( 4278): uid=10847(com.app.signage) CodecLooper identical 2 lines
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
D/SurfaceUtils( 4278): disconnecting from surface 0x7ac03eb010, reason disconnectFromSurface
D/SurfaceUtils( 4278): set up nativeWindow 0x7b479aa010 for 1920x544, color 0x7fa30c06, rotation 0, usage 0x20002900
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/chatty  ( 4278): uid=10847(com.app.signage) CodecLooper identical 5 lines
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/ImageReaderSurfaceProducer( 4278): ImageTextureEntry can't wait on the fence on Android < 33
I/flutter ( 4278): Safety timer: disposing old content widget: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Disposing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Current active schedule item ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Current schedule item ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Content widgets in memory: 5c1fa150-ec88-4b65-8693-003c728939e5, 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Content widget disposed for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Remaining widgets in memory: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): ====================================================
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): PlatformVideoWidget: Android video completed
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278): ID: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): Media Type: Video
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Moving from schedule item 1 to 2
I/flutter ( 4278): New schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): Current content: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Next content: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SimpleMediaWidget for nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): File Path: /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): Is Image: true, Is Video: false
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): LoggingService: Logged simple media display: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-06-01 05:21:31.295679Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 75e20d80-47ed-4274-aca9-0251f2a182b5, logDatetime: 2025-06-01 05:21:31.295679Z)
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:21:31.295679Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5}
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): SimpleMediaWidget: initState for nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ImageTimerWidget: initState for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ImageTimerWidget: starting timer for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png with duration 8 seconds
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Next content ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Displaying content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): PlatformVideoWidget: Disposing video player
I/flutter ( 4278): SimpleMediaWidget: dispose for FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/ExoPlayerImpl( 4278): Release afbf81d [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29] [media3.common, media3.exoplayer, media3.decoder, media3.datasource, media3.extractor]
D/SurfaceUtils( 4278): disconnecting from surface 0x7b479aa010, reason disconnectFromSurface
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1141, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 75e20d80-47ed-4274-aca9-0251f2a182b5, log_datetime: 2025-06-01T05:21:31.295679+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): Safety timer: disposing old content widget: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Disposing content widget for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Current active schedule item ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Current schedule item ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Content widgets in memory: 104a97fe-2928-4a35-b785-fbdc83765845, 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Content widget disposed for schedule item: 104a97fe-2928-4a35-b785-fbdc83765845
I/flutter ( 4278): Remaining widgets in memory: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ImageTimerWidget: timer completed for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ID: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): Media Type: Image
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Moving from schedule item 2 to 3
I/flutter ( 4278): New schedule item: 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): Current content: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Next content: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SimpleMediaWidget for 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): File Path: /storage/emulated/0/Android/data/com.app.signage/files/signage/content/37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): Is Image: false, Is Video: true
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): LoggingService: Logged simple media display: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: d696c03d-c48b-420e-9857-917a226024c4, logDatetime: 2025-06-01 05:21:39.369512Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: d696c03d-c48b-420e-9857-917a226024c4, logDatetime: 2025-06-01 05:21:39.369512Z)
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:21:39.369512Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: d696c03d-c48b-420e-9857-917a226024c4}
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): SimpleMediaWidget: initState for 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/ExoPlayerImpl( 4278): Init fdaa2af [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29]
I/flutter ( 4278): Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
D/MediaCodec( 4278):  name = OMX.qcom.video.decoder.avc
I/OMXClient( 4278): IOmx service obtained
I/flutter ( 4278): Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
D/SurfaceUtils( 4278): connecting to surface 0x7b477a6010, reason connectToSurface
I/MediaCodec( 4278): [OMX.qcom.video.decoder.avc] setting surface generation to 4380682
D/SurfaceUtils( 4278): disconnecting from surface 0x7b477a6010, reason connectToSurface(reconnect)
D/SurfaceUtils( 4278): connecting to surface 0x7b477a6010, reason connectToSurface(reconnect)
I/ExtendedACodec( 4278): setupVideoDecoder()
I/flutter ( 4278): Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
I/ExtendedACodec( 4278): Decoder will be in frame by frame mode
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1142, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: d696c03d-c48b-420e-9857-917a226024c4, log_datetime: 2025-06-01T05:21:39.369512+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Next content ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Displaying content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
D/SurfaceUtils( 4278): set up nativeWindow 0x7b477a6010 for 1920x540, color 0x7fa30c06, rotation 0, usage 0x20002900
I/flutter ( 4278): ImageTimerWidget: dispose for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): ImageTimerWidget: canceling timer for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278): SimpleMediaWidget: dispose for nzdeliveryapp_digi-eyeline-1920x540.png
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/chatty  ( 4278): uid=10847(com.app.signage) CodecLooper identical 2 lines
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/MediaCodec( 4278):  name = OMX.google.aac.decoder
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
I/OMXClient( 4278): IOmx service obtained
I/ACodec  ( 4278): codec does not support config priority (err -2147483648)
I/ACodec  ( 4278): codec does not support config priority (err -2147483648)
I/ACodec  ( 4278): codec does not support config operating rate (err -2147483648)
W/ExtendedACodec( 4278): Failed to get extension for extradata parameter
D/SurfaceUtils( 4278): set up nativeWindow 0x7b477a6010 for 1920x544, color 0x7fa30c06, rotation 0, usage 0x20002900
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/chatty  ( 4278): uid=10847(com.app.signage) CodecLooper identical 4 lines
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/AudioTrack( 4278): set(): streamType -1, sampleRate 48000, format 0x1, channelMask 0x3, frameCount 15376, flags #0, notificationFrames 0, sessionId 3617, transferType 3, uid -1, pid -1
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
D/AudioTrack( 4278): Uid 10847 AudioTrack::setVolume left 1.000000 right 1.000000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/OnePlusAudioTrackInjector( 4278): play() packageName: com.app.signage
I/OnePlusAudioTrackInjector( 4278): bufferSizeInBytes:61504, sampleRate:48000
E/AudioSystem-JNI( 4278): Command failed for android_media_AudioSystem_setParameters: -1
E/AudioSystem-JNI( 4278): Command failed for android_media_AudioSystem_setParameters: -1
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::start 
D/ImageReaderSurfaceProducer( 4278): ImageTextureEntry can't wait on the fence on Android < 33
D/AudioTrack( 4278): getTimestamp_l(1429): device stall time corrected using current time 109281754065185
W/AudioTrack( 4278): getTimestamp_l(1429): retrograde timestamp time corrected, 109281757621123 < 109281764031539
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/com.app.signag( 4278): Background concurrent copying GC freed 36040(8180KB) AllocSpace objects, 139(9372KB) LOS objects, 72% free, 4567KB/16MB, paused 186us total 109.474ms
I/flutter ( 4278): PlayerControllerService: Timer tick, checking internet connection
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): PlayerControllerService: Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/flutter ( 4278): PlayerControllerService: System health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 179584, memory_available: 3731512, memory_used: 7721456, memory_usage_percentage: 97.73, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 68G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:21:42.374522Z}
I/flutter ( 4278): [SupabaseService] Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515 to 2025-06-01T05:21:42.375Z
I/flutter ( 4278): [SupabaseService] Health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 179584, memory_available: 3731512, memory_used: 7721456, memory_usage_percentage: 97.73, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 68G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:21:42.374522Z}
I/flutter ( 4278): PlayerControllerService: Successfully updated screen details
I/flutter ( 4278): PlayerControllerService: Received realtime update payload: PostgresChangePayload(schema: public, table: screens, commitTimestamp: 2025-06-01 05:21:43.080Z, eventType: PostgresChangeEvent.update, newRow: {code: 01295, created_at: 2025-04-28T04:30:09.978743+00:00, end_time: 22:00:00, health: {app_version: 0.1.0, build_number: 1, external_storage_available: 44G, external_storage_total: 111G, external_storage_usage_percentage: 61, external_storage_used: 68G, internal_storage_available: 44G, internal_storage_total: 111G, internal_storage_usage_percentage: 61, internal_storage_used: 67G, memory_available: 3731512, memory_free: 179584, memory_total: 7901040, memory_usage_percentage: 97.73, memory_used: 7721456, package_name: com.app.signage, platform: Android, timestamp: 2025-06-01T05:21:42.374522Z}, id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, is_deleted: false, is_registered: true, last_ping_at: 2025-06-01T05:21:42.375+00:00, location: Main Office Building 1, name: Lobby Screen 1, site_email: , start_time: 08:00:00,
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: New row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: Old row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: New update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: Old update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: New is_deleted: false
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: No Updates
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): stop(1429): called with 241664 frames delivered
I/flutter ( 4278): Safety timer: disposing old content widget: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Disposing content widget for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Current active schedule item ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Current schedule item ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Content widgets in memory: 75e20d80-47ed-4274-aca9-0251f2a182b5, d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Content widget disposed for schedule item: 75e20d80-47ed-4274-aca9-0251f2a182b5
I/flutter ( 4278): Remaining widgets in memory: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): PlatformVideoWidget: Android video completed
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ID: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): Media Type: Video
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Finished all schedule items for campaign General Campaign
I/flutter ( 4278): Moving to next campaign...
I/flutter ( 4278): === MOVING TO NEXT VALID CAMPAIGN ===
I/flutter ( 4278): Current campaign index: 0
I/flutter ( 4278): Total campaigns: 3
I/flutter ( 4278): Checking campaign 1: Weather Campaign (trigger_type: 3)
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): set(): streamType -1, sampleRate 48000, format 0x1, channelMask 0x3, frameCount 15376, flags #0, notificationFrames 0, sessionId 3617, transferType 3, uid -1, pid -1
D/AudioTrack( 4278): Uid 10847 AudioTrack::setVolume left 1.000000 right 1.000000
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
I/flutter ( 4278): Device location obtained: lat=23.0121691, lon=72.5576054
I/flutter ( 4278): Fetching weather data from: https://api.openweathermap.org/data/2.5/weather?lat=23.0121691&lon=72.5576054&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
I/flutter ( 4278): Weather API response: 34.33°C
I/flutter ( 4278): Weather check: campaign=Weather Campaign, temp=34.33, range=20.0-40.0, inRange=true
I/flutter ( 4278): Campaign Weather Campaign meets trigger conditions!
I/flutter ( 4278): Loaded 1 schedule items for campaign Weather Campaign
I/flutter ( 4278):   [0] Weather
I/flutter ( 4278): === MOVED TO CAMPAIGN Weather Campaign ===
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: Weather
I/flutter ( 4278): ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: Weather
I/flutter ( 4278): Current content: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Next content: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: Weather
I/flutter ( 4278): ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Type: SlideShow
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SlideShowWidget for Weather
I/flutter ( 4278): ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): LoggingService: Logged slide display: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): SlideShowWidget: initState for Weather
I/flutter ( 4278): SlideShowWidget: Building content with design dimensions: 1920.0 x 1080.0
I/flutter ( 4278): SlideShowWidget: Actual screen dimensions: 731.4285714285714 x 411.42857142857144
I/flutter ( 4278): SlideShowWidget: Original playlist position: (0.0, 0.0), size: 1920.0 x 1080.0, zIndex: 1
I/flutter ( 4278): SlideShowWidget: Expected scaled position: (0.0, 0.0), size: 731.4285714285714 x 411.42857142857144
I/flutter ( 4278): SlideShowWidget: Original API position: (809.828125, 214.828125), size: 876.25 x 387.5, zIndex: 3
I/flutter ( 4278): SlideShowWidget: Creating GLOBAL API data controller for all APIWidgets in this slide
I/flutter ( 4278): ApiDataController: Created with apiUrl: https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric, apiDataPreviewDuration: 0
I/flutter ( 4278): ApiDataController: Fetching data from https://api.openweathermap.org/data/2.5/weather?lat=17&lon=17&appid=29b1d3623a51675e8ed3c1d3fa13c0f0&units=metric
I/flutter ( 4278): ApiDataController: Setting up refresh timer for every 5 minutes
I/flutter ( 4278): SlideShowWidget: GLOBAL API data controller created with apiDataPreviewDuration: 0
I/flutter ( 4278): SlideShowWidget: Expected scaled API position: (308.5059523809524, 81.83928571428572), size: 333.80952380952385 x 147.61904761904762
I/flutter ( 4278): SlideShowWidget: Original API position: (824.15625, 634.328125), size: 858.75 x 117.5, zIndex: 4
I/flutter ( 4278): SlideShowWidget: Expected scaled API position: (313.9642857142857, 241.64880952380955), size: 327.14285714285717 x 44.761904761904766
I/flutter ( 4278): SlideShowWidget: Original API position: (263.32916259765625, 258.32509358723956), size: 500.0 x 500.0, zIndex: 5
I/flutter ( 4278): SlideShowWidget: Expected scaled API position: (100.31587146577382, 98.40955946180556), size: 190.47619047619048 x 190.47619047619048
I/flutter ( 4278): Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): SlideShowWidget: Player screen size: 731.4285714285714x411.42857142857144
I/flutter ( 4278): SlideShowWidget: Design size: 1920.0 x 1080.0
I/flutter ( 4278): SlideShowWidget: Scaling factors: horizontal=0.380952380952381, vertical=0.380952380952381
I/flutter ( 4278): SlideShowWidget: Building scaled widgets with:
I/flutter ( 4278):   - Design dimensions: 1920.0 x 1080.0
I/flutter ( 4278):   - Screen dimensions: 731.4285714285714 x 411.42857142857144
I/flutter ( 4278):   - Scale factors: 0.380952380952381 x 0.380952380952381
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
I/flutter ( 4278):   - Scaled: left=0.0, top=0.0, width=731.4285714285714, height=411.42857142857144
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
I/flutter ( 4278):   - Scaled: left=308.5059523809524, top=81.83928571428572, width=333.80952380952385, height=147.61904761904762
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
I/flutter ( 4278):   - Scaled: left=313.9642857142857, top=241.64880952380955, width=327.14285714285717, height=44.761904761904766
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
I/flutter ( 4278):   - Scaled: left=100.31587146577382, top=98.40955946180556, width=190.47619047619048, height=190.47619047619048
I/flutter ( 4278): PlaylistWidget: initState for 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): PlaylistWidget: loading item weather-cloud.mp4 at index 0
I/flutter ( 4278): APIWidget: initState for 5e11d095-702b-457c-bcec-db2adc7e5e91
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
I/flutter ( 4278): APIWidget: Dimensions - width: 876.25, height: 387.5
I/flutter ( 4278): APIWidget: Showing loading state
I/flutter ( 4278): APIWidget: initState for 27a76786-c68e-4be7-9857-826c3a968cc4
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
I/flutter ( 4278): APIWidget: Dimensions - width: 858.75, height: 117.5
I/flutter ( 4278): APIWidget: Showing loading state
I/flutter ( 4278): APIWidget: initState for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: true, hasError: false, isInitialized: false, contentWidgets: 0
I/flutter ( 4278): APIWidget: Dimensions - width: 500.0, height: 500.0
I/flutter ( 4278): APIWidget: Showing loading state
I/flutter ( 4278): Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): PlaylistWidget: Building with dimensions 1920.0 x 1080.0
I/ExoPlayerImpl( 4278): Init e92d169 [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29]
D/MediaCodec( 4278):  name = OMX.qcom.video.decoder.avc
I/OMXClient( 4278): IOmx service obtained
I/flutter ( 4278): Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Next content ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
D/SurfaceUtils( 4278): connecting to surface 0x7b47812010, reason connectToSurface
I/MediaCodec( 4278): [OMX.qcom.video.decoder.avc] setting surface generation to 4380683
D/SurfaceUtils( 4278): disconnecting from surface 0x7b47812010, reason connectToSurface(reconnect)
D/SurfaceUtils( 4278): connecting to surface 0x7b47812010, reason connectToSurface(reconnect)
I/flutter ( 4278): ApiDataController: API Response received with status 200
I/flutter ( 4278): ApiDataController: Received 1 records from API
I/flutter ( 4278): ApiDataController: API data fetched successfully, waiting for all APIWidgets to initialize
I/flutter ( 4278): APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
I/flutter ( 4278): APIWidget: Building content widgets for record index 0
I/ExtendedACodec( 4278): setupVideoDecoder()
I/flutter ( 4278): APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
I/flutter ( 4278): APIWidget: Content item details - subtype: api.text, dataField: weather.description, placeholderUrl: 
I/flutter ( 4278): APIWidget: Extracting field "weather.description" (parts: weather, description)
I/flutter ( 4278): APIWidget: Found value for "weather.description": clear sky (String)
I/flutter ( 4278): APIWidget: Widget dimensions - width: 876.25, height: 387.5
I/flutter ( 4278): APIWidget: Building text widget with dataValue: clear sky (String)
I/flutter ( 4278): APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: weather.description, apiMapping: {weather.description: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
I/flutter ( 4278): APIWidget: Text content: "clear sky"
I/flutter ( 4278): APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 120px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
I/flutter ( 4278): APIWidget: Parsing color: rgba(255, 255, 255, 1)
I/flutter ( 4278): APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
I/flutter ( 4278): APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
I/flutter ( 4278): APIWidget: Raw fontSize value: 120px (String)
I/flutter ( 4278): APIWidget: Parsed font size: 120.0
I/flutter ( 4278): APIWidget: Font weight: bold -> FontWeight.w700
I/flutter ( 4278): APIWidget: Text alignment: center -> TextAlign.center
I/flutter ( 4278): APIWidget: Creating text widget with content: "clear sky" at size 876.25 x 387.5
I/flutter ( 4278): APIWidget: Added api.text widget with size 876.25x387.5
I/flutter ( 4278): APIWidget: Built 1 content widgets
I/flutter ( 4278): APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
I/flutter ( 4278): APIWidget: Building content widgets for record index 0
I/flutter ( 4278): APIWidget: Processing content item: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
I/flutter ( 4278): APIWidget: Content item details - subtype: api.text, dataField: main.temp, placeholderUrl: 
I/flutter ( 4278): APIWidget: Extracting field "main.temp" (parts: main, temp)
I/flutter ( 4278): APIWidget: Found value for "main.temp": 29.93 (double)
I/flutter ( 4278): APIWidget: Widget dimensions - width: 858.75, height: 117.5
I/flutter ( 4278): APIWidget: Building text widget with dataValue: 29.93 (double)
I/flutter ( 4278): APIWidget: Content config: {style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}, subtype: api.text, dataField: main.temp, apiMapping: {main.temp: true}, placeholder: API Data, currentIndex: 0, placeholderUrl: }
I/flutter ( 4278): APIWidget: Text content: "29.93"
I/flutter ( 4278): APIWidget: Text style: {color: rgba(255, 255, 255, 1), fontSize: 96px, textAlign: center, fontFamily: Helvetica, sans-serif, fontWeight: bold, verticalAlign: middle, backgroundColor: transparent}
I/flutter ( 4278): APIWidget: Parsing color: rgba(255, 255, 255, 1)
I/flutter ( 4278): APIWidget: Parsed rgba color: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
I/flutter ( 4278): APIWidget: Text color from string: rgba(255, 255, 255, 1) -> Color(alpha: 1.0000, red: 1.0000, green: 1.0000, blue: 1.0000, colorSpace: ColorSpace.sRGB)
I/flutter ( 4278): APIWidget: Raw fontSize value: 96px (String)
I/flutter ( 4278): APIWidget: Parsed font size: 96.0
I/flutter ( 4278): APIWidget: Font weight: bold -> FontWeight.w700
I/flutter ( 4278): APIWidget: Text alignment: center -> TextAlign.center
I/flutter ( 4278): APIWidget: Creating text widget with content: "29.93" at size 858.75 x 117.5
I/flutter ( 4278): APIWidget: Added api.text widget with size 858.75x117.5
I/flutter ( 4278): APIWidget: Built 1 content widgets
I/flutter ( 4278): APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
I/flutter ( 4278): APIWidget: Building content widgets for record index 0
I/flutter ( 4278): APIWidget: Processing content item: {style: {color: #000000, fontSize: 32px, textAlign: center, fontFamily: Inter, sans-serif, fontWeight: normal, verticalAlign: middle, backgroundColor: transparent}, subtype: api.image, dataField: weather.icon, placeholder: API Data, placeholderUrl: http://192.168.29.13:5000/src/assets/weather-icons/{weather.icon}.gif}
I/flutter ( 4278): APIWidget: Content item details - subtype: api.image, dataField: weather.icon, placeholderUrl: http://192.168.29.13:5000/src/assets/weather-icons/{weather.icon}.gif
I/flutter ( 4278): APIWidget: Extracting field "weather.icon" (parts: weather, icon)
I/flutter ( 4278): APIWidget: Found value for "weather.icon": 01d (String)
I/flutter ( 4278): APIWidget: Widget dimensions - width: 500.0, height: 500.0
I/flutter ( 4278): APIWidget: Building image widget with dataValue: 01d (String), placeholderUrl: http://192.168.29.13:5000/src/assets/weather-icons/{weather.icon}.gif
I/flutter ( 4278): APIWidget: dataValue is String: 01d
I/flutter ( 4278): APIWidget: URL validation check: 01d is invalid
I/ExtendedACodec( 4278): Decoder will be in frame by frame mode
I/flutter ( 4278): APIWidget: Found field pattern {weather.icon} in placeholder URL
I/flutter ( 4278): APIWidget: Using placeholder URL with substitution: http://192.168.29.13:5000/src/assets/weather-icons/{weather.icon}.gif -> http://192.168.29.13:5000/src/assets/weather-icons/01d.gif
I/flutter ( 4278): APIWidget: Loading image from URL: http://192.168.29.13:5000/src/assets/weather-icons/01d.gif
I/flutter ( 4278): APIWidget: Using direct streaming from remote server for image
I/flutter ( 4278): APIWidget: Image type: Regular image
I/flutter ( 4278): APIWidget: Image dimensions: 500.0 x 500.0
I/flutter ( 4278): APIWidget: Rebuilt content for record 0 after sync
I/flutter ( 4278): APIWidget: Rebuilt content for record 0 after sync
I/flutter ( 4278): APIWidget: Added api.image widget with size 500.0x500.0
I/flutter ( 4278): APIWidget: Built 1 content widgets
I/flutter ( 4278): APIWidget: Rebuilt content for record 0 after sync
I/flutter ( 4278): Displaying content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): SlideShowWidget: Player screen size: 731.4285714285714x411.42857142857144
I/flutter ( 4278): SlideShowWidget: Design size: 1920.0 x 1080.0
I/flutter ( 4278): SlideShowWidget: Scaling factors: horizontal=0.380952380952381, vertical=0.380952380952381
I/ExoPlayerImpl( 4278): Release fdaa2af [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29] [media3.common, media3.exoplayer, media3.decoder, media3.datasource, media3.extractor]
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/SurfaceUtils( 4278): set up nativeWindow 0x7b47812010 for 1920x1080, color 0x7fa30c06, rotation 0, usage 0x20002900
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/SurfaceUtils( 4278): disconnecting from surface 0x7b477a6010, reason disconnectFromSurface
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
D/SurfaceUtils( 4278): set up nativeWindow 0x7b47812010 for 1920x1088, color 0x7fa30c06, rotation 0, usage 0x20002900
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/chatty  ( 4278): uid=10847(com.app.signage) CodecLooper identical 4 lines
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/ImageReaderSurfaceProducer( 4278): ImageTextureEntry can't wait on the fence on Android < 33
I/flutter ( 4278): SlideShowWidget: Building scaled widgets with:
I/flutter ( 4278):   - Design dimensions: 1920.0 x 1080.0
I/flutter ( 4278):   - Screen dimensions: 731.4285714285714 x 411.42857142857144
I/flutter ( 4278):   - Scale factors: 0.380952380952381 x 0.380952380952381
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
I/flutter ( 4278):   - Scaled: left=0.0, top=0.0, width=731.4285714285714, height=411.42857142857144
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
I/flutter ( 4278):   - Scaled: left=308.5059523809524, top=81.83928571428572, width=333.80952380952385, height=147.61904761904762
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
I/flutter ( 4278):   - Scaled: left=313.9642857142857, top=241.64880952380955, width=327.14285714285717, height=44.761904761904766
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
I/flutter ( 4278):   - Scaled: left=100.31587146577382, top=98.40955946180556, width=190.47619047619048, height=190.47619047619048
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 876.25, height: 387.5
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 858.75, height: 117.5
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 500.0, height: 500.0
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): PlaylistWidget: Building with dimensions 1920.0 x 1080.0
I/flutter ( 4278): PlatformVideoWidget: Disposing video player
I/flutter ( 4278): SimpleMediaWidget: dispose for 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): ApiDataController: Checking if all widgets are ready - dataReady: true, videoWidgets: 0
I/flutter ( 4278): SlideShowWidget: All APIWidgets are ready, starting global timer
I/flutter ( 4278): ApiDataController: Starting global timer for all APIWidgets
I/flutter ( 4278): ApiDataController: Starting GLOBAL display timer - videoWidgets: 0, apiDataPreviewDuration: 0
I/flutter ( 4278): ApiDataController: Starting GLOBAL display timer for 15 seconds (no videos)
I/flutter ( 4278): APIWidget: Controller update received for 5e11d095-702b-457c-bcec-db2adc7e5e91
I/flutter ( 4278): APIWidget: Controller update received for 27a76786-c68e-4be7-9857-826c3a968cc4
I/flutter ( 4278): APIWidget: Controller update received for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 876.25, height: 387.5
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 858.75, height: 117.5
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 500.0, height: 500.0
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: 3cc80b6d-18d5-446a-b785-979c950c4d92, slideId: 80b479a8-d300-46ae-97d0-59653e52d73f, mediaId: null, logDatetime: 2025-06-01 05:21:47.697163Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: 3cc80b6d-18d5-446a-b785-979c950c4d92, slideId: 80b479a8-d300-46ae-97d0-59653e52d73f, mediaId: null, logDatetime: 2025-06-01 05:21:47.697163Z)
I/flutter ( 4278): [SupabaseService] Setting media_id to null for empty value
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:21:47.697163Z, campaign_id: 3cc80b6d-18d5-446a-b785-979c950c4d92, slide_id: 80b479a8-d300-46ae-97d0-59653e52d73f, media_id: null}
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1143, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: 3cc80b6d-18d5-446a-b785-979c950c4d92, slide_id: 80b479a8-d300-46ae-97d0-59653e52d73f, media_id: null, log_datetime: 2025-06-01T05:21:47.697163+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
I/flutter ( 4278): Safety timer: disposing old content widget: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Disposing content widget for schedule item: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Current active schedule item ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Current schedule item ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Content widgets in memory: d696c03d-c48b-420e-9857-917a226024c4, 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Content widget disposed for schedule item: d696c03d-c48b-420e-9857-917a226024c4
I/flutter ( 4278): Remaining widgets in memory: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): ApiDataController: GLOBAL timer expired (no videos), advancing to next record
I/flutter ( 4278): ApiDataController: All records displayed (1/1), disposing slide and advancing to next schedule item
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: Weather
I/flutter ( 4278): ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Type: SlideShow
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Finished all schedule items for campaign Weather Campaign
I/flutter ( 4278): Moving to next campaign...
I/flutter ( 4278): === MOVING TO NEXT VALID CAMPAIGN ===
I/flutter ( 4278): Current campaign index: 1
I/flutter ( 4278): Total campaigns: 3
I/flutter ( 4278): Checking campaign 2: One NZ Promo (trigger_type: 2)
I/flutter ( 4278): PlatformVideoWidget: Android video completed
I/flutter ( 4278): PlaylistWidget: end of playlist, calling onComplete
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: Weather
I/flutter ( 4278): ID: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Type: SlideShow
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Finished all schedule items for campaign Weather Campaign
I/flutter ( 4278): Moving to next campaign...
I/flutter ( 4278): === MOVING TO NEXT VALID CAMPAIGN ===
I/flutter ( 4278): Current campaign index: 1
I/flutter ( 4278): Total campaigns: 3
I/flutter ( 4278): Checking campaign 2: One NZ Promo (trigger_type: 2)
I/flutter ( 4278): PlayerControllerService: Timer tick, checking internet connection
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): PlayerControllerService: Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: System health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 136532, memory_available: 3651228, memory_used: 7764508, memory_usage_percentage: 98.27, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 67G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:22:12.348804Z}
I/flutter ( 4278): [SupabaseService] Updating screen details for screen f4feb5dc-8546-4ac6-8e07-3ae62cdb6515 to 2025-06-01T05:22:12.349606Z
I/flutter ( 4278): [SupabaseService] Health info: {app_version: 0.1.0, build_number: 1, package_name: com.app.signage, memory_total: 7901040, memory_free: 136532, memory_available: 3651228, memory_used: 7764508, memory_usage_percentage: 98.27, internal_storage_total: 111G, internal_storage_used: 67G, internal_storage_available: 44G, internal_storage_usage_percentage: 61, external_storage_total: 111G, external_storage_used: 67G, external_storage_available: 44G, external_storage_usage_percentage: 61, platform: Android, timestamp: 2025-06-01T05:22:12.348804Z}
I/flutter ( 4278): PlayerControllerService: Received realtime update payload: PostgresChangePayload(schema: public, table: screens, commitTimestamp: 2025-06-01 05:22:13.509Z, eventType: PostgresChangeEvent.update, newRow: {code: 01295, created_at: 2025-04-28T04:30:09.978743+00:00, end_time: 22:00:00, health: {app_version: 0.1.0, build_number: 1, external_storage_available: 44G, external_storage_total: 111G, external_storage_usage_percentage: 61, external_storage_used: 67G, internal_storage_available: 44G, internal_storage_total: 111G, internal_storage_usage_percentage: 61, internal_storage_used: 67G, memory_available: 3651228, memory_free: 136532, memory_total: 7901040, memory_usage_percentage: 98.27, memory_used: 7764508, package_name: com.app.signage, platform: Android, timestamp: 2025-06-01T05:22:12.348804Z}, id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, is_deleted: false, is_registered: true, last_ping_at: 2025-06-01T05:22:12.349606+00:00, location: Main Office Building 1, name: Lobby Screen 1, site_email: , start_time: 08:00:
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: New row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: Old row ID: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515
I/flutter ( 4278): PlayerControllerService: New update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: Old update GUID: 6bc84c2d-2c89-49fb-be55-b89ee8337cfe
I/flutter ( 4278): PlayerControllerService: New is_deleted: false
I/flutter ( 4278): ***************************************************************
I/flutter ( 4278): PlayerControllerService: No Updates
I/flutter ( 4278): PlayerControllerService: Successfully updated screen details
I/flutter ( 4278): Device location obtained: lat=23.0121691, lon=72.5576054
I/flutter ( 4278): Geofencing check: campaign=One NZ Promo, device=(23.0121691, 72.5576054), geofence=(23.0055, 72.45), radius=30000.0, inside=true
I/flutter ( 4278): Campaign One NZ Promo meets trigger conditions!
I/flutter ( 4278): Loaded 1 schedule items for campaign One NZ Promo
I/flutter ( 4278):   [0] one-nz-promo.png
I/flutter ( 4278): === MOVED TO CAMPAIGN One NZ Promo ===
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: one-nz-promo.png
I/flutter ( 4278): Current content: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Next content: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SimpleMediaWidget for one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): File Path: /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): Is Image: true, Is Video: false
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): LoggingService: Logged simple media display: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: 921d6313-06c6-461b-a7fb-b9c19abfef16, slideId: null, mediaId: 8780ae77-2859-48e7-88b9-e81aa57a86ca, logDatetime: 2025-06-01 05:22:18.614821Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: 921d6313-06c6-461b-a7fb-b9c19abfef16, slideId: null, mediaId: 8780ae77-2859-48e7-88b9-e81aa57a86ca, logDatetime: 2025-06-01 05:22:18.614821Z)
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:22:18.614821Z, campaign_id: 921d6313-06c6-461b-a7fb-b9c19abfef16, slide_id: null, media_id: 8780ae77-2859-48e7-88b9-e81aa57a86ca}
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): SimpleMediaWidget: initState for one-nz-promo.png
I/flutter ( 4278): ImageTimerWidget: initState for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): ImageTimerWidget: starting timer for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png with duration 8 seconds
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): SlideShowWidget: Player screen size: 731.4285714285714x411.42857142857144
I/flutter ( 4278): SlideShowWidget: Design size: 1920.0 x 1080.0
I/flutter ( 4278): SlideShowWidget: Scaling factors: horizontal=0.380952380952381, vertical=0.380952380952381
I/flutter ( 4278): SlideShowWidget: Building scaled widgets with:
I/flutter ( 4278):   - Design dimensions: 1920.0 x 1080.0
I/flutter ( 4278):   - Screen dimensions: 731.4285714285714 x 411.42857142857144
I/flutter ( 4278):   - Scale factors: 0.380952380952381 x 0.380952380952381
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=0.0, top=0.0, width=1920.0, height=1080.0
I/flutter ( 4278):   - Scaled: left=0.0, top=0.0, width=731.4285714285714, height=411.42857142857144
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=809.828125, top=214.828125, width=876.25, height=387.5
I/flutter ( 4278):   - Scaled: left=308.5059523809524, top=81.83928571428572, width=333.80952380952385, height=147.61904761904762
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=824.15625, top=634.328125, width=858.75, height=117.5
I/flutter ( 4278):   - Scaled: left=313.9642857142857, top=241.64880952380955, width=327.14285714285717, height=44.761904761904766
I/flutter ( 4278): SlideShowWidget: Scaling widget:
I/flutter ( 4278):   - Original: left=263.32916259765625, top=258.32509358723956, width=500.0, height=500.0
I/flutter ( 4278):   - Scaled: left=100.31587146577382, top=98.40955946180556, width=190.47619047619048, height=190.47619047619048
I/flutter ( 4278): PlaylistWidget: Building with dimensions 1920.0 x 1080.0
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 876.25, height: 387.5
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 858.75, height: 117.5
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): APIWidget: Building widget with state - isLoading: false, hasError: false, isInitialized: true, contentWidgets: 1
I/flutter ( 4278): APIWidget: Dimensions - width: 500.0, height: 500.0
I/flutter ( 4278): APIWidget: Displaying 1 content widgets
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1144, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: 921d6313-06c6-461b-a7fb-b9c19abfef16, slide_id: null, media_id: 8780ae77-2859-48e7-88b9-e81aa57a86ca, log_datetime: 2025-06-01T05:22:18.614821+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Next content ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): PlatformVideoWidget: Disposing video player
I/flutter ( 4278): PlaylistWidget: dispose for 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): APIWidget: dispose for 5e11d095-702b-457c-bcec-db2adc7e5e91
I/flutter ( 4278): APIWidget: No video controllers to clean up
I/flutter ( 4278): APIWidget: All resources cleaned up
I/flutter ( 4278): APIWidget: dispose for 27a76786-c68e-4be7-9857-826c3a968cc4
I/flutter ( 4278): APIWidget: No video controllers to clean up
I/flutter ( 4278): APIWidget: All resources cleaned up
I/flutter ( 4278): APIWidget: dispose for dce9c75d-bae9-4160-ac4d-0d274fe8dc96
I/flutter ( 4278): APIWidget: No video controllers to clean up
I/flutter ( 4278): APIWidget: All resources cleaned up
I/flutter ( 4278): SlideShowWidget: dispose for Weather
I/flutter ( 4278): SlideShowWidget: Disposing API data controller
I/flutter ( 4278): ApiDataController: Disposing
I/ExoPlayerImpl( 4278): Release e92d169 [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29] [media3.common, media3.exoplayer, media3.decoder, media3.datasource, media3.extractor]
D/SurfaceUtils( 4278): disconnecting from surface 0x7b47812010, reason disconnectFromSurface
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0
I/flutter ( 4278): Safety timer: disposing old content widget: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Disposing content widget for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Current active schedule item ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Current schedule item ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Content widgets in memory: 80b479a8-d300-46ae-97d0-59653e52d73f, 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Content widget disposed for schedule item: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Remaining widgets in memory: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ImageTimerWidget: timer completed for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Content completed, moving to next item
I/flutter ( 4278): Completed widget details:
I/flutter ( 4278): Name: one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): Media Type: Image
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Finished all schedule items for campaign One NZ Promo
I/flutter ( 4278): Moving to next campaign...
I/flutter ( 4278): === MOVING TO NEXT VALID CAMPAIGN ===
I/flutter ( 4278): Current campaign index: 2
I/flutter ( 4278): Total campaigns: 3
I/flutter ( 4278): Checking campaign 0: General Campaign (trigger_type: 1)
I/flutter ( 4278): Default loop campaign: General Campaign - displaying
I/flutter ( 4278): Campaign General Campaign meets trigger conditions!
I/flutter ( 4278): Loaded 4 schedule items for campaign General Campaign
I/flutter ( 4278):   [0] BetterWithPepsi.mp4
I/flutter ( 4278):   [1] FBN2407-VRefresh-BlackcurrantYuZu-1920x540-v1.mp4
I/flutter ( 4278):   [2] nzdeliveryapp_digi-eyeline-1920x540.png
I/flutter ( 4278):   [3] 37327 Skinny Eyeline digital 1920x540.mp4
I/flutter ( 4278): === MOVED TO CAMPAIGN General Campaign ===
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: BetterWithPepsi.mp4
I/flutter ( 4278): ID: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: BetterWithPepsi.mp4
I/flutter ( 4278): Current content: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Next content: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: BetterWithPepsi.mp4
I/flutter ( 4278): ID: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SimpleMediaWidget for BetterWithPepsi.mp4
I/flutter ( 4278): ID: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): File Path: /storage/emulated/0/Android/data/com.app.signage/files/signage/content/BetterWithPepsi.mp4
I/flutter ( 4278): Is Image: false, Is Video: true
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): LoggingService: Logged simple media display: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-06-01 05:22:26.676122Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slideId: null, mediaId: 5c1fa150-ec88-4b65-8693-003c728939e5, logDatetime: 2025-06-01 05:22:26.676122Z)
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:22:26.676122Z, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5}
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): SimpleMediaWidget: initState for BetterWithPepsi.mp4
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/ExoPlayerImpl( 4278): Init 4bb8f7c [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29]
I/flutter ( 4278): Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
D/MediaCodec( 4278):  name = OMX.qcom.video.decoder.avc
I/OMXClient( 4278): IOmx service obtained
I/flutter ( 4278): Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
D/SurfaceUtils( 4278): connecting to surface 0x7aa54e5010, reason connectToSurface
I/MediaCodec( 4278): [OMX.qcom.video.decoder.avc] setting surface generation to 4380684
D/SurfaceUtils( 4278): disconnecting from surface 0x7aa54e5010, reason connectToSurface(reconnect)
D/SurfaceUtils( 4278): connecting to surface 0x7aa54e5010, reason connectToSurface(reconnect)
I/ExtendedACodec( 4278): setupVideoDecoder()
I/ExtendedACodec( 4278): Decoder will be in frame by frame mode
I/flutter ( 4278): Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Next content ID: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Displaying content widget for schedule item: 5c1fa150-ec88-4b65-8693-003c728939e5
D/SurfaceUtils( 4278): set up nativeWindow 0x7aa54e5010 for 1920x540, color 0x7fa30c06, rotation 0, usage 0x20002900
I/flutter ( 4278): ImageTimerWidget: dispose for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): ImageTimerWidget: canceling timer for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): SimpleMediaWidget: dispose for one-nz-promo.png
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/MediaCodec( 4278):  name = OMX.google.aac.decoder
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/OMXClient( 4278): IOmx service obtained
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
I/ACodec  ( 4278): codec does not support config priority (err -2147483648)
I/ACodec  ( 4278): codec does not support config priority (err -2147483648)
I/ACodec  ( 4278): codec does not support config operating rate (err -2147483648)
W/ExtendedACodec( 4278): Failed to get extension for extradata parameter
D/SurfaceUtils( 4278): set up nativeWindow 0x7aa54e5010 for 1920x544, color 0x7fa30c06, rotation 0, usage 0x20002900
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/chatty  ( 4278): uid=10847(com.app.signage) CodecLooper identical 2 lines
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/AudioTrack( 4278): set(): streamType -1, sampleRate 48000, format 0x1, channelMask 0x3, frameCount 15376, flags #0, notificationFrames 0, sessionId 3633, transferType 3, uid -1, pid -1
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
W/AMessage( 4278): the pointer from->u.stringValue and to->u is not null
D/AudioTrack( 4278): Uid 10847 AudioTrack::setVolume left 1.000000 right 1.000000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/OnePlusAudioTrackInjector( 4278): play() packageName: com.app.signage
I/OnePlusAudioTrackInjector( 4278): bufferSizeInBytes:61504, sampleRate:48000
E/AudioSystem-JNI( 4278): Command failed for android_media_AudioSystem_setParameters: -1
E/AudioSystem-JNI( 4278): Command failed for android_media_AudioSystem_setParameters: -1
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::start 
D/ImageReaderSurfaceProducer( 4278): ImageTextureEntry can't wait on the fence on Android < 33
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
D/AudioTrack( 4278): getTimestamp_l(1431): device stall time corrected using current time 109329090796053
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1145, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: fe2fddc8-f709-4b54-b6c9-c95bf1ed9852, slide_id: null, media_id: 5c1fa150-ec88-4b65-8693-003c728939e5, log_datetime: 2025-06-01T05:22:26.676122+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
W/AudioTrack( 4278): getTimestamp_l(1431): retrograde timestamp time corrected, 109329093626365 < 109329102034178
W/MapperHal( 4278): buffer descriptor with invalid usage bits 0x2000
I/flutter ( 4278): Device location obtained: lat=23.0121691, lon=72.5576054
I/flutter ( 4278): Geofencing check: campaign=One NZ Promo, device=(23.0121691, 72.5576054), geofence=(23.0055, 72.45), radius=30000.0, inside=true
I/flutter ( 4278): Campaign One NZ Promo meets trigger conditions!
I/flutter ( 4278): Loaded 1 schedule items for campaign One NZ Promo
I/flutter ( 4278):   [0] one-nz-promo.png
I/flutter ( 4278): === MOVED TO CAMPAIGN One NZ Promo ===
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Playing next schedule item: one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Starting transition to next content: one-nz-promo.png
I/flutter ( 4278): Current content: 80b479a8-d300-46ae-97d0-59653e52d73f
I/flutter ( 4278): Next content: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Current widget that has finished playing:
I/flutter ( 4278): Name: one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Type: SimpleMedia
I/flutter ( 4278): ====================================================
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Creating SimpleMediaWidget for one-nz-promo.png
I/flutter ( 4278): ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): File Path: /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): Is Image: true, Is Video: false
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Storing content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): LoggingService: Logged simple media display: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 1
I/flutter ( 4278): LoggingService: Processing 1 proof of play logs
I/flutter ( 4278): LoggingService: Sending proof of play log to Supabase: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: 921d6313-06c6-461b-a7fb-b9c19abfef16, slideId: null, mediaId: 8780ae77-2859-48e7-88b9-e81aa57a86ca, logDatetime: 2025-06-01 05:22:28.799574Z)
I/flutter ( 4278): [SupabaseService] Logging proof of play: ProofOfPlay(id: null, screenId: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaignId: 921d6313-06c6-461b-a7fb-b9c19abfef16, slideId: null, mediaId: 8780ae77-2859-48e7-88b9-e81aa57a86ca, logDatetime: 2025-06-01 05:22:28.799574Z)
I/flutter ( 4278): [SupabaseService] Proof of play data: {screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, log_datetime: 2025-06-01T05:22:28.799574Z, campaign_id: 921d6313-06c6-461b-a7fb-b9c19abfef16, slide_id: null, media_id: 8780ae77-2859-48e7-88b9-e81aa57a86ca}
I/flutter ( 4278): Starting transition animation
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): SimpleMediaWidget: initState for one-nz-promo.png
I/flutter ( 4278): ImageTimerWidget: initState for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): ImageTimerWidget: starting timer for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png with duration 8 seconds
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Completing transition
I/flutter ( 4278): Current content ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Next content ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Displaying content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ImageTimerWidget: image loaded for /storage/emulated/0/Android/data/com.app.signage/files/signage/content/one-nz-promo.png
I/flutter ( 4278): PlatformVideoWidget: Disposing video player
I/flutter ( 4278): SimpleMediaWidget: dispose for BetterWithPepsi.mp4
I/flutter ( 4278): [SupabaseService] Proof of play log response: [{id: 1146, screen_id: f4feb5dc-8546-4ac6-8e07-3ae62cdb6515, campaign_id: 921d6313-06c6-461b-a7fb-b9c19abfef16, slide_id: null, media_id: 8780ae77-2859-48e7-88b9-e81aa57a86ca, log_datetime: 2025-06-01T05:22:28.799574+00:00}]
I/flutter ( 4278): LoggingService: Proof of play log result: true
I/ExoPlayerImpl( 4278): Release 4bb8f7c [AndroidXMedia3/1.4.1] [OnePlus5, ONEPLUS A5000, OnePlus, 29] [media3.common, media3.exoplayer, media3.decoder, media3.datasource, media3.extractor]
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::pause 
I/OnePlusAudioTrackInjector( 4278): stop() packageName: com.app.signage, sampleRate: 48000
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/AudioTrack( 4278): ClientUid 10847 AudioTrack::stop 
D/SurfaceUtils( 4278): disconnecting from surface 0x7aa54e5010, reason disconnectFromSurface
I/flutter ( 4278): Safety timer: disposing old content widget: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): ====================================================
I/flutter ( 4278): Disposing content widget for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Current active schedule item ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Current schedule item ID: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Content widgets in memory: 8780ae77-2859-48e7-88b9-e81aa57a86ca, 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): Content widget disposed for schedule item: 8780ae77-2859-48e7-88b9-e81aa57a86ca
I/flutter ( 4278): Remaining widgets in memory: 5c1fa150-ec88-4b65-8693-003c728939e5
I/flutter ( 4278): ====================================================
I/flutter ( 4278): LoggingService: Processing queue - Activities: 0, Proof of Play: 0

Application finished.